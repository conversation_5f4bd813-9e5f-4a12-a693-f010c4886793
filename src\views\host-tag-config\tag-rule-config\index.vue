<template>
  <el-card class="tag-rule-config">
    <!-- 查询条件 -->
    <el-form :model="searchForm" ref="searchForm" label-position="left" inline>
      <div class="flex-container">
        <el-row>
          <el-form-item label="CDN类型" prop="cdn_type">
            <el-select v-model="searchForm.cdn_type" placeholder="请选择CDN类型" style="width:150px" clearable>
              <el-option label="PCDN" value="2"></el-option>
              <el-option label="LCDN" value="4"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="标签键" prop="tag_key">
            <el-select
              v-model="searchForm.tag_key"
              clearable
              placeholder="请选择标签键"
              style="width: 180px"
              @change="onTagKeyChange"
            >
              <el-option v-for="itm in tagKeyOptions" :key="itm.tag_key" :value="itm.tag_key" :label="itm.tag_key"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="标签值" prop="tag_value">
            <el-select
              v-model="searchForm.tag_value"
              clearable
              placeholder="请选择标签值"
              style="width: 180px"
            >
              <el-option v-for="value in tagValueOptions" :key="value" :value="value" :label="value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-row>
      </div>
    </el-form>

    <!-- 操作按钮 -->
    <el-row style="margin-bottom: 20px;">
      <el-button type="primary" @click="showAddDialog">新增标签规则</el-button>
    </el-row>

    <!-- 表格 -->
    <el-table
      :data="tableData"
      v-loading="querying"
      border
      class="table-style"
    >
      <el-table-column prop="id" label="ID" align="center" width="80"></el-table-column>
      <el-table-column prop="cdn_type" label="CDN类型" align="center" width="100">
        <template slot-scope="scope">
          <span>{{ getCdnTypeName(scope.row.cdn_type) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="module" label="模块" align="center" width="120"></el-table-column>
      <el-table-column prop="tag_infos" label="标签信息" align="center" min-width="200">
        <template slot-scope="scope">
          <div v-if="scope.row.tag_infos && scope.row.tag_infos.length">
            <div v-for="(tag, index) in scope.row.tag_infos" :key="index" class="tag-info-item">
              {{ tag.tag_key }}: {{ tag.tag_value }}
            </div>
          </div>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column prop="resource_group" label="资源分组" align="center" width="120"></el-table-column>
      <el-table-column prop="rap_tag_infos" label="RAP标签信息" align="center" min-width="250">
        <template slot-scope="scope">
          <div v-if="scope.row.rap_tag_infos && scope.row.rap_tag_infos.length">
            <el-tag
              v-for="(tag, index) in formatRapTags(scope.row.rap_tag_infos)"
              :key="index"
              size="mini"
              style="margin: 2px;"
            >
              {{ bizLabelMap[tag] || tag }}
            </el-tag>
          </div>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column prop="lvs_mode" label="LVS模式" align="center" width="100">
        <template slot-scope="scope">
          <span>{{ getLvsModeLabel(scope.row.lvs_mode) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="port" label="端口" align="center" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.port || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" align="center" min-width="150"></el-table-column>
      <el-table-column prop="operator" label="操作人" align="center" width="120"></el-table-column>
      <el-table-column label="操作" align="center" width="150" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleEdit(scope.row)">修改</el-button>
          <el-button size="mini" type="text" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-row style="text-align: center; padding-top: 20px">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.page_size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      ></el-pagination>
    </el-row>

    <!-- 新增/编辑弹窗 -->
    <tag-rule-dialog
      :visible="dialogVisible"
      :is-edit="isEdit"
      :current-row-data="currentRowData"
      @close="dialogVisible = false"
      @refresh="onSearch"
    ></tag-rule-dialog>

  </el-card>
</template>

<script>
import http from "@/views/host-tag-config/http.js"
import tagRuleDialog from "./dialog/tagRuleDialog.vue"

export default {
  name: "tag-rule-config",
  components: {
    tagRuleDialog
  },
  data() {
    return {
      querying: false,
      tableData: [],
      dialogVisible: false,
      isEdit: false,
      currentRowData: {},
      pagination: {
        total: 0,
        page: 1,
        page_size: 20,
      },
      searchForm: {
        cdn_type: '',
        tag_key: '',
        tag_value: '',
      },
      // 标签选项数据
      tagOptions: [],
      tagKeyOptions: [],
      tagValueOptions: [],
      bizLabelMap: {},
    };
  },
  created() {
    this.loadTagOptions();
    this.loadBizLabelOptions();
  },
  mounted() {
    this.onSearch()
  },
  methods: {
    // 加载标签选项数据
    async loadTagOptions() {
      try {
        const tagRes = await http.getTagList({ page_size: 1000000 });
        if (tagRes) {
          this.tagOptions = tagRes.data?.items || [];
          // 提取唯一的标签键
          this.tagKeyOptions = this.getUniqueTagKeys(this.tagOptions);
          // 初始化时显示所有标签值
          this.tagValueOptions = this.getAllTagValues(this.tagOptions);
        }
      } catch (error) {
        console.error("加载标签选项数据失败:", error);
      }
    },
    // 加载业务标签选项
    async loadBizLabelOptions() {
      try {
        const res = await http.getBizLabelOptions();
        if (res) {
          this.bizLabelMap = (res?.map(itm => ({
            label: itm.tagCnName,
            value: itm.tagCode
          })) || []).reduce((prev, cur) => {
            prev[cur.value] = cur.label;
            return prev;
          }, {});
        }
      } catch (error) {
        console.error('加载业务标签选项失败:', error);
      }
    },
    // 获取唯一的标签键
    getUniqueTagKeys(tagOptions) {
      const uniqueKeys = [];
      const keySet = new Set();

      tagOptions.forEach(tag => {
        if (tag.tag_key && !keySet.has(tag.tag_key)) {
          keySet.add(tag.tag_key);
          uniqueKeys.push({ tag_key: tag.tag_key });
        }
      });

      return uniqueKeys;
    },

    // 获取所有可用的标签值选项
    getAllTagValues(tagOptions) {
      const values = [];
      const valueSet = new Set();

      tagOptions.forEach(tag => {
        if (tag.tag_value_list && Array.isArray(tag.tag_value_list)) {
          tag.tag_value_list.forEach(value => {
            if (value && !valueSet.has(value)) {
              valueSet.add(value);
              values.push(value);
            }
          });
        }
      });

      return values;
    },

    // 根据选中的标签键获取对应的标签值选项
    getTagValuesByKey(tagKey) {
      if (!tagKey) {
        // 未选择标签键时，返回所有标签值
        return this.getAllTagValues(this.tagOptions);
      }

      const values = [];
      const valueSet = new Set();

      this.tagOptions.forEach(tag => {
        if (tag.tag_key === tagKey && tag.tag_value_list && Array.isArray(tag.tag_value_list)) {
          tag.tag_value_list.forEach(value => {
            if (value && !valueSet.has(value)) {
              valueSet.add(value);
              values.push(value);
            }
          });
        }
      });

      return values;
    },

    // 标签键变化时的处理
    onTagKeyChange() {
      // 清空标签值
      this.searchForm.tag_value = "";
      // 更新标签值选项：如果有选中的标签键则过滤，否则显示所有
      this.tagValueOptions = this.getTagValuesByKey(this.searchForm.tag_key);
    },

    onSearch() {
      this.pagination.page = 1;
      this.query();
    },
    resetSearch() {
      this.$refs.searchForm.resetFields();
      // 重置标签值选项为所有可用值
      this.tagValueOptions = this.getAllTagValues(this.tagOptions);
      this.onSearch();
    },
    async query() {
      let params = {
        ...this.searchForm,
        page: this.pagination.page,
        page_size: this.pagination.page_size,
      };

      // 过滤空值
      Object.keys(params).forEach(key => {
        if (params[key] === '' || params[key] === null || params[key] === undefined) {
          delete params[key];
        }
      });

      this.querying = true;
      try {
        const res = await http.getTagRuleList(params);
        if (res && res.code === 100000) {
          if (res.data && Array.isArray(res.data.items)) {
            this.tableData = res.data.items;
            this.pagination.total = res.data.total || 0;
            this.pagination.page = res.data.page || 1;
            this.pagination.page_size = res.data.page_size || 20;
          } else {
            console.error('返回数据格式异常:', res.data);
            this.tableData = [];
            this.pagination.total = 0;
          }
        } else {
          this.$message.error((res && res.message) || "查询失败");
        }
      } catch (error) {
        console.error('API请求错误:', error);
        this.$message.error("查询失败");
      } finally {
        this.querying = false;
      }
    },
    handleSizeChange(val) {
      this.pagination.page = 1;
      this.pagination.page_size = val;
      this.query();
    },
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.query();
    },
    showAddDialog() {
      this.isEdit = false;
      this.currentRowData = {};
      this.dialogVisible = true;
    },
    handleEdit(row) {
      this.isEdit = true;
      this.currentRowData = JSON.parse(JSON.stringify(row));
      this.dialogVisible = true;
    },
    async handleDelete(row) {
      this.$confirm('确定要删除这个标签规则吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          // 获取操作人信息
          const operator = window.localStorage.getItem("userInfo") || "system";
          const res = await http.deleteTagRule(row.id, { operator });
          if (res && res.code === 100000) {
            this.$message.success('删除成功');
            this.onSearch();
          } else {
            this.$message.error((res && res.message) || "删除失败");
          }
        } catch (error) {
          console.error('删除标签规则错误:', error);
          this.$message.error("删除失败");
        }
      }).catch(() => {
        // 用户取消删除
      });
    },
    getCdnTypeName(cdnType) {
      if (!cdnType) return '--';

      // CDN类型可能是字符串 "1,2" 的形式
      const types = cdnType.toString().split(',').map(type => {
        switch(type.trim()) {
          case '2': return 'PCDN';
          case '4': return 'LCDN';
          default: return type;
        }
      });

      return types.join(', ');
    },

    // 格式化RAP标签显示
    formatRapTags(rapTagInfos) {
      if (!rapTagInfos) return [];

      // 如果是数组，直接返回
      if (Array.isArray(rapTagInfos)) {
        return rapTagInfos;
      }

      // 如果是字符串，按逗号分割
      if (typeof rapTagInfos === 'string') {
        return rapTagInfos.split(',').map(tag => tag.trim()).filter(tag => tag);
      }

      return [];
    },

    // 获取LVS模式标签
    getLvsModeLabel(lvsMode) {
      if (!lvsMode) return '--';

      switch(lvsMode.toString()) {
        case '1': return '普通模式';
        case '2': return '隧道模式';
        default: return lvsMode;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.tag-rule-config {
  .el-pagination {
    margin-top: 20px;
    text-align: right;
  }
}

.flex-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-style {
  margin-top: 20px;
  width: 100%;
}

.tag-info-item {
  margin-bottom: 4px;
  padding: 2px 6px;
  background-color: #f5f7fa;
  border-radius: 3px;
  font-size: 12px;
}
</style>
